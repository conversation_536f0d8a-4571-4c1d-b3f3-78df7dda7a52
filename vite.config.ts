import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],

  // Optimize for static deployment
  build: {
    // Generate source maps for debugging
    sourcemap: true,

    // Optimize chunk splitting
    rollupOptions: {
      output: {
        manualChunks: {
          // Separate vendor chunks for better caching
          vendor: ['react', 'react-dom'],
          utils: ['./src/utils/index.ts'],
        },
      },
    },

    // Optimize assets
    assetsInlineLimit: 4096, // Inline assets smaller than 4kb
    cssCodeSplit: true, // Split CSS into separate files

    // Target modern browsers for smaller bundles
    target: 'es2015',

    // Minify for production
    minify: 'esbuild',
  },

  // Optimize dependencies
  optimizeDeps: {
    include: ['react', 'react-dom'],
  },

  // Configure base path for deployment
  base: './',

  // Preview server configuration
  preview: {
    port: 4173,
    host: true,
  },

  // Development server configuration
  server: {
    port: 5173,
    host: true,
    open: true,
  },
})
