import React from 'react';
import { Header } from './Header';
import { Footer } from './Footer';
import { cn } from '../../utils';

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
  showHeader?: boolean;
  showFooter?: boolean;
}

export const Layout: React.FC<LayoutProps> = ({ 
  children, 
  className,
  showHeader = true,
  showFooter = true 
}) => {
  return (
    <div className={cn('min-h-screen flex flex-col', className)}>
      {showHeader && <Header />}
      
      <main className="flex-1">
        {children}
      </main>
      
      {showFooter && <Footer />}
    </div>
  );
};
