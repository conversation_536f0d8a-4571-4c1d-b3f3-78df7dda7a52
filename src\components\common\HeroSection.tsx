import React from 'react';
import { Button } from '../ui';
import { cn } from '../../utils';

interface HeroSectionProps {
  className?: string;
}

export const HeroSection: React.FC<HeroSectionProps> = ({ className }) => {
  return (
    <section className={cn('bg-gradient-to-br from-primary-50 to-secondary-50 py-20 lg:py-32', className)}>
      <div className="container-custom">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-left">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-secondary-900 font-heading mb-6 animate-fade-in">
              Master English with{' '}
              <span className="text-primary-600">Interactive Learning</span>
            </h1>
            <p className="text-xl text-secondary-600 mb-8 max-w-2xl animate-slide-up">
              Transform your English skills with our innovative ESL platform. 
              Personalized lessons, real-time feedback, and engaging content 
              designed for learners at every level.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start animate-slide-up">
              <Button size="lg" className="px-8 py-4">
                Start Learning Free
              </Button>
              <Button variant="outline" size="lg" className="px-8 py-4">
                Watch Demo
              </Button>
            </div>
            
            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 mt-12 pt-8 border-t border-secondary-200">
              <div className="text-center lg:text-left">
                <div className="text-2xl font-bold text-primary-600">10K+</div>
                <div className="text-sm text-secondary-600">Active Students</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl font-bold text-primary-600">95%</div>
                <div className="text-sm text-secondary-600">Success Rate</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl font-bold text-primary-600">50+</div>
                <div className="text-sm text-secondary-600">Countries</div>
              </div>
            </div>
          </div>

          {/* Visual */}
          <div className="relative">
            <div className="relative z-10 bg-white rounded-2xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-300">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
                <div className="space-y-3">
                  <div className="h-4 bg-secondary-200 rounded w-3/4"></div>
                  <div className="h-4 bg-primary-200 rounded w-1/2"></div>
                  <div className="h-4 bg-secondary-200 rounded w-5/6"></div>
                  <div className="h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                    <span className="text-primary-600 font-medium">Interactive Lesson</span>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="h-6 bg-green-100 rounded flex items-center justify-center">
                      <span className="text-green-600 text-sm">✓ Correct</span>
                    </div>
                    <div className="h-6 bg-secondary-100 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Background decoration */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-2xl transform -rotate-3 opacity-20"></div>
            
            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 w-16 h-16 bg-primary-500 rounded-full opacity-20 animate-bounce-gentle"></div>
            <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-secondary-500 rounded-full opacity-20 animate-bounce-gentle" style={{ animationDelay: '1s' }}></div>
          </div>
        </div>
      </div>
    </section>
  );
};
