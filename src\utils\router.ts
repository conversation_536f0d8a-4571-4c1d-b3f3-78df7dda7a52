// Simple hash-based router for static deployment
export type Route = {
  path: string;
  component: React.ComponentType;
  title: string;
};

export class SimpleRouter {
  private routes: Map<string, Route> = new Map();
  private currentRoute: string = '/';
  private listeners: Array<(route: string) => void> = [];

  constructor() {
    // Listen for hash changes
    window.addEventListener('hashchange', this.handleHashChange.bind(this));
    window.addEventListener('load', this.handleHashChange.bind(this));
  }

  addRoute(path: string, component: React.ComponentType, title: string) {
    this.routes.set(path, { path, component, title });
  }

  navigate(path: string) {
    window.location.hash = path;
  }

  getCurrentRoute(): string {
    return this.currentRoute;
  }

  getCurrentComponent(): React.ComponentType | null {
    const route = this.routes.get(this.currentRoute);
    return route ? route.component : null;
  }

  onRouteChange(callback: (route: string) => void) {
    this.listeners.push(callback);
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private handleHashChange() {
    const hash = window.location.hash.slice(1) || '/';
    const route = this.routes.has(hash) ? hash : '/';
    
    if (route !== this.currentRoute) {
      this.currentRoute = route;
      const routeData = this.routes.get(route);
      
      // Update document title
      if (routeData) {
        document.title = `${routeData.title} - ESL Learn`;
      }
      
      // Notify listeners
      this.listeners.forEach(callback => callback(route));
    }
  }
}

// Create a singleton router instance
export const router = new SimpleRouter();
