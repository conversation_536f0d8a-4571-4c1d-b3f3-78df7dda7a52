import React, { useState, useEffect } from 'react';
import { router } from '../../utils/router';
import { LandingPage, AboutPage, ContactPage } from '../../pages';

// Register routes
router.addRoute('/', LandingPage, 'Home');
router.addRoute('/about', AboutPage, 'About Us');
router.addRoute('/contact', ContactPage, 'Contact');

export const Router: React.FC = () => {
  const [, setCurrentRoute] = useState(router.getCurrentRoute());

  useEffect(() => {
    const unsubscribe = router.onRouteChange((route) => {
      setCurrentRoute(route);
    });

    return unsubscribe;
  }, []);

  const CurrentComponent = router.getCurrentComponent();

  if (!CurrentComponent) {
    // 404 fallback - redirect to home
    router.navigate('/');
    return null;
  }

  return <CurrentComponent />;
};
