import React from 'react';
import type { CardProps } from '../../types';
import { cn } from '../../utils';

const cardVariants = {
  default: 'bg-white border border-secondary-200 shadow-sm',
  elevated: 'bg-white shadow-lg border-0',
  outlined: 'bg-white border-2 border-primary-200 shadow-none',
};

export const Card: React.FC<CardProps> = ({
  title,
  children,
  className,
  variant = 'default',
  ...props
}) => {
  return (
    <div
      className={cn(
        'rounded-xl p-6 transition-shadow duration-200',
        cardVariants[variant],
        className
      )}
      {...props}
    >
      {title && (
        <h3 className="text-lg font-semibold text-secondary-900 mb-4">
          {title}
        </h3>
      )}
      {children}
    </div>
  );
};
