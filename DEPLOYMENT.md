# ESL Learn - Deployment Guide

This guide explains how to deploy your ESL Learn static website to various hosting platforms.

## Build Process

The project is optimized for static deployment with the following features:

- **Hash-based routing** for client-side navigation
- **Optimized bundle splitting** for better caching
- **Source maps** for debugging
- **Modern ES2015+ target** for smaller bundles
- **CSS code splitting** for faster loading

## Building for Production

```bash
# Standard production build
npm run build

# Build with production optimizations
npm run build:production

# Build with bundle analysis
npm run build:analyze
```

The build output will be in the `dist/` directory.

## Deployment Options

### 1. Netlify (Recommended)

1. Connect your repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `dist`
4. Deploy automatically on push

**Netlify Configuration (_netlify.toml):**
```toml
[build]
  command = "npm run build"
  publish = "dist"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 2. Vercel

1. Connect your repository to Vercel
2. Framework preset: Vite
3. Build command: `npm run build`
4. Output directory: `dist`

### 3. GitHub Pages

1. Build the project: `npm run build`
2. Push the `dist` folder to `gh-pages` branch
3. Enable GitHub Pages in repository settings

**GitHub Actions Workflow (.github/workflows/deploy.yml):**
```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dist
```

### 4. Firebase Hosting

1. Install Firebase CLI: `npm install -g firebase-tools`
2. Initialize: `firebase init hosting`
3. Set public directory: `dist`
4. Configure as SPA: Yes
5. Deploy: `firebase deploy`

### 5. AWS S3 + CloudFront

1. Build the project: `npm run build`
2. Upload `dist` contents to S3 bucket
3. Configure S3 for static website hosting
4. Set up CloudFront distribution
5. Configure error pages to redirect to `index.html`

## Environment Variables

For different environments, you can create:

- `.env.development` - Development environment
- `.env.production` - Production environment
- `.env.local` - Local overrides (not committed)

Example:
```env
VITE_API_URL=https://api.esllearn.com
VITE_APP_NAME=ESL Learn
```

## Performance Optimizations

The build includes several optimizations:

- **Code splitting**: Vendor libraries separated for better caching
- **Tree shaking**: Unused code eliminated
- **Asset optimization**: Images and fonts optimized
- **CSS purging**: Unused Tailwind classes removed
- **Compression**: Gzip compression enabled

## Testing the Build

```bash
# Preview the production build locally
npm run preview

# Run type checking
npm run type-check

# Run linting
npm run lint
```

## Troubleshooting

### Hash Routing Issues

If navigation doesn't work on your hosting platform:

1. Ensure your server redirects all routes to `index.html`
2. Check that the base path is configured correctly in `vite.config.ts`

### Build Errors

Common issues and solutions:

- **TypeScript errors**: Run `npm run type-check` to identify issues
- **Import errors**: Check file paths and exports
- **Memory issues**: Increase Node.js memory: `NODE_OPTIONS="--max-old-space-size=4096" npm run build`

### Performance Issues

- Use `npm run build:analyze` to identify large bundles
- Consider lazy loading for large components
- Optimize images and assets

## Browser Support

The build targets modern browsers (ES2015+):
- Chrome 61+
- Firefox 60+
- Safari 11+
- Edge 16+

For older browser support, update the `target` in `vite.config.ts`.

## Security Considerations

- All builds are static files (no server-side code)
- No sensitive data should be included in the build
- Use environment variables for configuration
- Enable HTTPS on your hosting platform

## Monitoring and Analytics

Consider adding:
- Google Analytics or similar
- Error tracking (Sentry, LogRocket)
- Performance monitoring
- User feedback tools

## Next Steps

After deployment:
1. Set up custom domain
2. Configure SSL certificate
3. Set up monitoring
4. Add analytics
5. Test on different devices and browsers
