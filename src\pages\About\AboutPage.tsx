import React from 'react';
import { Layout } from '../../components/layout';
import { <PERSON>, Button } from '../../components/ui';
import type { PageProps } from '../../types';
import { companyStats } from '../../data';

export const AboutPage: React.FC<PageProps> = ({ className }) => {
  return (
    <Layout className={className}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 to-secondary-50 py-20">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-secondary-900 font-heading mb-6">
              About ESL Learn
            </h1>
            <p className="text-xl text-secondary-600 mb-8">
              We're passionate about making English language learning accessible, 
              engaging, and effective for students around the world.
            </p>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-secondary-900 font-heading mb-6">
                Our Mission
              </h2>
              <p className="text-lg text-secondary-600 mb-6">
                At ESL Learn, we believe that language learning should be an exciting journey, 
                not a daunting task. Our mission is to provide innovative, interactive, and 
                personalized English learning experiences that empower students to achieve 
                their goals.
              </p>
              <p className="text-lg text-secondary-600 mb-8">
                Through cutting-edge technology and proven pedagogical methods, we create 
                an environment where learners can practice, improve, and gain confidence 
                in their English skills.
              </p>
              <Button size="lg">Learn More About Our Approach</Button>
            </div>
            <div className="relative">
              <div className="bg-primary-100 rounded-2xl p-8">
                <div className="grid grid-cols-2 gap-6">
                  {companyStats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-2xl font-bold text-primary-600 mb-2">
                        {stat.value}
                      </div>
                      <div className="text-sm text-secondary-600">
                        {stat.label}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-secondary-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 font-heading mb-4">
              Our Values
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              These core values guide everything we do and shape the learning experience we provide.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">🎯</span>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                Excellence
              </h3>
              <p className="text-secondary-600">
                We strive for excellence in every aspect of our platform, from content quality 
                to user experience.
              </p>
            </Card>

            <Card className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">🤝</span>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                Accessibility
              </h3>
              <p className="text-secondary-600">
                Quality English education should be accessible to everyone, regardless of 
                background or location.
              </p>
            </Card>

            <Card className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">🚀</span>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                Innovation
              </h3>
              <p className="text-secondary-600">
                We continuously innovate to create more effective and engaging learning 
                experiences.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-white">
        <div className="container-custom">
          <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-8 md:p-12 text-center">
            <h3 className="text-2xl md:text-3xl font-bold text-secondary-900 mb-4">
              Ready to Start Your Learning Journey?
            </h3>
            <p className="text-lg text-secondary-600 mb-8 max-w-2xl mx-auto">
              Join thousands of students who have already improved their English skills 
              with our comprehensive learning platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="px-8 py-3">
                Get Started Today
              </Button>
              <Button variant="outline" size="lg" className="px-8 py-3">
                Contact Us
              </Button>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};
